use jni::objects::{JO<PERSON>, JThrowable};
use jni::{objects::<PERSON><PERSON><PERSON><PERSON>, JNIEnv};
use std::{any::Any, error::Error, fmt::Debug, panic::AssertUnwindSafe};
pub mod error;
pub mod jni_bridge;
use error::Result;

pub fn is_jni_bridge_inited() -> bool {
    jni_bridge::JavaClasses::inited()
}

pub fn get_double_array(env: &mut JNIEnv, array: jni::sys::jdoubleArray) -> Option<Vec<f64>> {
    if array.is_null() {
        return None;
    }

    let raw_array = array;
    let length = match env.get_array_length(raw_array) {
        Ok(len) => len as usize,
        Err(_) => return None,
    };

    if length == 0 {
        return Some(Vec::new());
    }

    let mut buffer = vec![0.0; length];
    match env.get_double_array_region(raw_array, 0, &mut buffer) {
        Ok(_) => Some(buffer),
        Err(_) => None,
    }
}

pub fn create_big_decimal(env: &mut JNIEnv, value: f64) -> jni::sys::jobject {
    let value_str = value.to_string();
    let j_string = env.new_string(value_str).expect("Failed to create Java string");

    let big_decimal_class = env.find_class("java/math/BigDecimal").expect("Failed to find BigDecimal class");

    let result = env
        .new_object(big_decimal_class, "(Ljava/lang/String;)V", &[JValue::Object(j_string.into())])
        .expect("Failed to create BigDecimal object");

    result.into_raw()
}

pub fn crate_jstring(env: &mut JNIEnv, value: &str) -> Result<jstring> {
    let j_string = env.new_string(value)?;
    Ok(j_string.into())
}

pub fn handle_unwinded(err: Box<dyn Any + Send>) {
    // default handling:
    //  * caused by Interrupted/TaskKilled: do nothing but just print a message.
    //  * other reasons: wrap it into a RuntimeException and throw.
    //  * if another error happens during handling, kill the whole JVM instance.
    let recover = || {
        let panic_message = panic_message::panic_message(&err);

        // throw jvm runtime exception
        if jni_exception_check!()? {
            let throwable = jni_exception_occurred!()?;
            jni_exception_clear!()?;
            throw_runtime_exception(panic_message, throwable.as_obj())?;
        } else {
            throw_runtime_exception(panic_message, JObject::null())?;
        };
        Ok(())
    };
    recover().unwrap_or_else(|err: Box<dyn Error>| {
        jni_fatal_error!("Error recovering from panic, cannot resume: {err:?}");
    });
}

pub fn handle_unwinded_scope<T: Default, E: Debug>(scope: impl FnOnce() -> Result<T, E>) -> T {
    std::panic::catch_unwind(AssertUnwindSafe(|| match scope() {
        Ok(v) => v,
        Err(err) => {
            panic!("Error: {:?}", err);
        }
    }))
    .unwrap_or_else(|err| {
        handle_unwinded(err);
        T::default()
    })
}

pub fn throw_runtime_exception(msg: &str, cause: JObject) -> Result<()> {
    let msg = jni_new_string!(msg)?;
    let e = jni_new_object!(JavaRuntimeException(msg.as_obj(), cause))?;

    if let Err(err) = jni_throw!(JThrowable::from(e.as_obj())) {
        jni_fatal_error!("Error throwing RuntimeException, cannot result: {err:?}");
    }
    Ok(())
}
