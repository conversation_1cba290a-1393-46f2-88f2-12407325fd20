use crate::config::DwTestItemConfig;
use bumpalo::Bump;
use ck_provider::{Ck<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ck<PERSON>roviderImpl};
use common::ck::ck_sink::SinkHandler;
use common::dto::dwd::die_detail_parquet::DieDetailParquet;
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::dwd::test_item_detail_row::TestItemDetailRow;
use common::dto::ods::test_item_data_parquet::TestItemDataParquet;
use common::dwd::dwd_service::DwdService;
use common::dwd::model::value::die_test_info::DieTestInfo;
use common::dwd::sink::test_item_detail_handler::TestItemDetailHandler;
use common::dwd::table::distributed::test_item_detail_service::TestItemDetailService;
use common::dwd::table::test_item_detail_common_service::TestItemDetailCommonService;
use common::model::constant::upload_type::UploadType;
use common::model::key::{die_key::DieKey, wafer_key::WaferKey};
use common::repository::mysql::test_num_force_zero_config_repository::TestNumForceZeroConfigRepository;
use common::utils::path;
use mysql_provider::MySqlConfig;
use parquet_provider::parquet_provider::write_parquet;
use std::collections::HashMap;
use std::error::Error;
use std::time::Duration;
// Corresponding to Scala file:
// /dataware/dataware-dw/dataware-dw-test-item/src/main/scala/com/guwave/onedata/dataware/dw/testItem/spark/dwd/service/impl/CpDwdTestItemService.scala

/// CpDwdTestItemService handles CP (Contact Probe) stage DWD layer test item processing
/// This service orchestrates the calculation of test item details for the CP test stage
#[derive(Debug, Clone)]
pub struct CpDwdTestItemService {
    test_item_detail_result_partition: i32,
    test_item_detail_service: TestItemDetailService,
    mysql_config: MySqlConfig,
    properties: DwTestItemConfig,
}

/// Calculation result containing broadcast maps and datasets
/// Corresponds to the return type of calculate method in Scala (line 30)
#[derive(Debug)]
pub struct CpDwdCalculationResult {
    pub file_detail_map: HashMap<u64, FileDetail>,
    pub die_detail: Vec<DieDetailParquet>,
    pub test_item_detail: Vec<SubTestItemDetail>,
}

impl CpDwdTestItemService {
    /// Create new CpDwdTestItemService
    ///
    /// Corresponds to: CpDwdTestItemService.scala:27 (case class constructor)
    /// case class CpDwdTestItemService(properties: DwTestItemProperties, testItemDetailResultPartition: Int)
    pub fn new(test_item_detail_result_partition: i32, test_area: String) -> Self {
        let config: DwTestItemConfig = DwTestItemConfig::get_config().unwrap();
        Self {
            test_item_detail_result_partition,
            test_item_detail_service: TestItemDetailService::new(test_area),
            mysql_config: config.get_mysql_config(),
            properties: config,
        }
    }

    /// Calculate CP DWD test item details
    ///
    /// Corresponds to: CpDwdTestItemService.scala:30-90
    /// def calculate(spark: SparkSession, dieDetailSource: Dataset[DieDetail], testItemData: Dataset[TestItemData],
    ///              waferKey: WaferKey, testArea: String, executeMode: String, fileCategory: String,
    ///              ckSinkType: String, runMode: String): (Broadcast[Map[lang.Long, FileDetail]], Dataset[DieDetail], Dataset[SubTestItemDetail])
    pub async fn calculate(
        &self,
        die_detail_source: Vec<DieDetailParquet>,
        test_item_data: Vec<TestItemDataParquet>,
        wafer_key: &WaferKey,
        test_area: String,
        execute_mode: String,
        file_category: String,
        ck_sink_type: String,
        run_mode: String,
    ) -> Result<CpDwdCalculationResult, Box<dyn Error>> {
        let bump = Bump::new();
        log::info!("当前正在计算的waferNo: {}", wafer_key);
        let die_detail = die_detail_source;
        let test_num_force_zero_test_program_list = TestNumForceZeroConfigRepository::new(self.mysql_config.clone())
            .await
            .read_test_num_force_zero_test_program_list(
                wafer_key.customer.clone(),
                wafer_key.sub_customer.clone(),
                wafer_key.factory.clone(),
                wafer_key.factory_site.clone(),
                test_area.to_string(),
                wafer_key.device_id.clone(),
                wafer_key.test_stage.clone(),
                die_detail
                    .iter()
                    .map(|die| die.TEST_PROGRAM.clone().unwrap())
                    .collect::<Vec<String>>(),
                UploadType::AUTO.to_string(),
            )
            .await
            .unwrap();

        let die_test_info_map = self.build_die_test_info_broadcast_map(&die_detail).unwrap();
        let file_detail_map = self.build_file_detail_broadcast_map(&die_detail).unwrap();
        let test_item_detail = self
            .test_item_detail_service
            .calculate_cp_test_item_detail(
                test_item_data,
                file_category.as_str(),
                &die_test_info_map,
                self.properties.get_need_multiply_scale(wafer_key.customer.as_str()),
                self.properties.standard_units.as_str(),
                self.properties.get_clear_invalid_data_flag(wafer_key.customer.as_str()),
                &test_num_force_zero_test_program_list,
            )
            .await
            .unwrap();

        log::info!("test_item_detail length: {:?}", test_item_detail.len());

        let test_item_detail_with_file_info = bump.alloc(
            test_item_detail
                .iter()
                .map(|test_item_detail| {
                    let file_detail = file_detail_map.get(&(test_item_detail.FILE_ID.unwrap() as u64)).unwrap();
                    TestItemDetailCommonService::get_test_item_detail_with_file_info(test_item_detail, &file_detail)
                })
                .collect::<Result<Vec<TestItemDetailRow>, Box<dyn Error>>>()?,
        );

        let test_item_detail_path = path::get_dwd_wafer_path(
            &self.properties.cp_test_item_detail_result_dir,
            &test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        log::info!("写入parquet文件到路径: {}", test_item_detail_path);

        // 确保目录存在
        if let Some(parent) = std::path::Path::new(&test_item_detail_path).parent() {
            std::fs::create_dir_all(parent).map_err(|e| {
                Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("创建目录失败: {}", e)))
                    as Box<dyn Error>
            })?;
        }

        // 写入parquet文件
        let parquet_file_path = format!("{}/test_item_detail.parquet", test_item_detail_path);
        write_parquet(&parquet_file_path, &test_item_detail, None);
        log::info!("成功写入parquet文件: {}", parquet_file_path);

        // 写入到ClickHouse
        let ck_config = self.properties.get_ck_config(self.properties.dwd_db_name.as_str());
        let ck_provider = CkProviderImpl::new(ck_config);
        let test_item_detail_handler =
            TestItemDetailHandler::new(self.properties.dwd_db_name.clone(), self.properties.insert_cluster_table);

        log::info!("开始写入ClickHouse，数据量: {}", &test_item_detail_with_file_info.len());
        ck_provider
            .insert(
                &format!("{}.{}", test_item_detail_handler.db_name(), test_item_detail_handler.table_name()),
                &test_item_detail_with_file_info,
            )
            .await
            .map_err(|e| {
                Box::new(std::io::Error::new(std::io::ErrorKind::Other, format!("写入ClickHouse失败: {}", e)))
                    as Box<dyn Error>
            })?;
        log::info!("成功写入ClickHouse");

        drop(bump);
        Ok(CpDwdCalculationResult { file_detail_map, die_detail, test_item_detail })
    }

    /// Build die test info map from die details for broadcasting
    /// This method creates the broadcast map used in the calculation
    ///
    /// Corresponds to: CpDwdTestItemService.scala:43
    /// val dieTestInfoMap = sc.broadcast(dieDetail.map(buildDieTestInfo).rdd.distinct().collect.toMap)
    fn build_die_test_info_broadcast_map(
        &self,
        die_details: &[DieDetailParquet],
    ) -> Result<HashMap<DieKey, DieTestInfo>, Box<dyn Error>> {
        DwdService::build_die_test_info_broadcast_map(die_details)
    }

    /// Build file detail broadcast map from die details using FileDetailService
    /// This method creates the file detail map used for merging with test item details
    ///
    /// Corresponds to: CpDwdTestItemService.scala:45
    /// val fileDetailMap = FileDetailService().broadcastFileDetail(spark, dieDetail)
    fn build_file_detail_broadcast_map(
        &self,
        die_details: &[DieDetailParquet],
    ) -> Result<HashMap<u64, FileDetail>, Box<dyn Error>> {
        let file_detail_service = common::dto::dwd::file_detail::FileDetailService::new();
        file_detail_service.broadcast_file_detail(die_details)
    }

    /// Get test item detail result partition count
    pub fn get_test_item_detail_result_partition(&self) -> i32 {
        self.test_item_detail_result_partition
    }
}
