use chrono::Utc;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use parquet_provider::parquet_provider::{read_parquet, write_parquet};
use std::collections::HashMap;
use tracing::Level;
use tracing_subscriber::fmt::format::FmtSpan;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 设置日志
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_span_events(FmtSpan::FULL)
        .with_file(true)
        .with_line_number(true)
        .init();

    // 创建测试数据
    let test_data = create_test_data();

    // 测试parquet写入和读取
    test_parquet_operations(&test_data).await?;

    Ok(())
}

fn create_test_data() -> Vec<SubTestItemDetail> {
    let mut condition_set = HashMap::new();
    condition_set.insert("TEMP".to_string(), "25C".to_string());

    let mut ecid_extra = HashMap::new();
    ecid_extra.insert("ECID_KEY1".to_string(), "ECID_VALUE1".to_string());

    let mut long_attribute_set = HashMap::new();
    long_attribute_set.insert("LONG_ATTR1".to_string(), 12345i64);

    let mut string_attribute_set = HashMap::new();
    string_attribute_set.insert("STRING_ATTR1".to_string(), "STRING_VALUE1".to_string());

    let mut float_attribute_set = HashMap::new();
    float_attribute_set.insert("FLOAT_ATTR1".to_string(), 3.14f64);

    let mut efuse_extra = HashMap::new();
    efuse_extra.insert("EFUSE_KEY1".to_string(), "EFUSE_VALUE1".to_string());

    vec![SubTestItemDetail {
        FILE_ID: Some(12345),
        ONLINE_RETEST: Some(0),
        MAX_OFFLINE_RETEST: Some(0),
        MAX_ONLINE_RETEST: Some(0),
        IS_DIE_FIRST_TEST: Some(1),
        IS_DIE_FINAL_TEST: Some(1),
        IS_FIRST_TEST: Some(1),
        IS_FINAL_TEST: Some(1),
        IS_FIRST_TEST_IGNORE_TP: Some(1),
        IS_FINAL_TEST_IGNORE_TP: Some(1),
        IS_DUP_FIRST_TEST: Some(0),
        IS_DUP_FINAL_TEST: Some(0),
        IS_DUP_FIRST_TEST_IGNORE_TP: Some(0),
        IS_DUP_FINAL_TEST_IGNORE_TP: Some(0),
        TEST_SUITE: Some("TEST_SUITE_1".to_string()),
        CONDITION_SET: Some(condition_set),
        TEST_NUM: Some(1001),
        TEST_TXT: Some("VDD_TEST".to_string()),
        TEST_ITEM: Some("VDD_VOLTAGE".to_string()),
        IS_DIE_FIRST_TEST_ITEM: Some(1),
        TESTITEM_TYPE: Some("P".to_string()),
        TEST_FLG: Some("PASS".to_string()),
        PARM_FLG: Some("NORMAL".to_string()),
        TEST_STATE: Some("EXECUTED".to_string()),
        TEST_VALUE: Some(3.3),
        UNITS: Some("V".to_string()),
        TEST_RESULT: Some(1),
        ORIGIN_TEST_VALUE: Some(3.3),
        ORIGIN_UNITS: Some("V".to_string()),
        TEST_ORDER: Some(1),
        ALARM_ID: Some("".to_string()),
        OPT_FLG: Some("".to_string()),
        RES_SCAL: Some(0),
        NUM_TEST: Some(1),
        LLM_SCAL: Some(0),
        HLM_SCAL: Some(0),
        LO_LIMIT: Some(3.0),
        HI_LIMIT: Some(3.6),
        ORIGIN_HI_LIMIT: Some(3.6),
        ORIGIN_LO_LIMIT: Some(3.0),
        C_RESFMT: Some("%.3f".to_string()),
        C_LLMFMT: Some("%.3f".to_string()),
        C_HLMFMT: Some("%.3f".to_string()),
        LO_SPEC: Some(3.0),
        HI_SPEC: Some(3.6),
        HBIN_NUM: Some(1),
        SBIN_NUM: Some(1),
        SBIN_PF: Some("P".to_string()),
        SBIN_NAM: Some("PASS".to_string()),
        HBIN_PF: Some("P".to_string()),
        HBIN_NAM: Some("PASS".to_string()),
        HBIN: Some("1".to_string()),
        SBIN: Some("1".to_string()),
        TEST_HEAD: Some(1),
        PART_FLG: Some("NORMAL".to_string()),
        PART_ID: Some("PART_001".to_string()),
        C_PART_ID: Some(1),
        ECID: Some("ECID_001".to_string()),
        ECID_EXT: Some("".to_string()),
        ECID_EXTRA: Some(ecid_extra),
        IS_STANDARD_ECID: Some(1),
        X_COORD: Some(100),
        Y_COORD: Some(200),
        DIE_X: Some(10),
        DIE_Y: Some(20),
        TEST_TIME: Some(1000),
        PART_TXT: Some("PART_TEXT".to_string()),
        PART_FIX: Some("PART_FIX".to_string()),
        SITE: Some(1),
        TOUCH_DOWN_ID: Some(1),
        WAFER_LOT_ID: Some("TEST_WAFER_LOT".to_string()),
        WAFER_ID: Some("WAFER_001".to_string()),
        WAFER_NO: Some("W001".to_string()),
        RETICLE_T_X: Some(0),
        RETICLE_T_Y: Some(0),
        RETICLE_X: Some(0),
        RETICLE_Y: Some(0),
        SITE_ID: Some("SITE_001".to_string()),
        VECT_NAM: Some("VECTOR_001".to_string()),
        TIME_SET: Some("TIMESET_001".to_string()),
        NUM_FAIL: Some(0),
        FAIL_PIN: Some("".to_string()),
        CYCL_CNT: Some(1),
        REPT_CNT: Some(1),
        LONG_ATTRIBUTE_SET: Some(long_attribute_set),
        STRING_ATTRIBUTE_SET: Some(string_attribute_set),
        FLOAT_ATTRIBUTE_SET: Some(float_attribute_set),
        UID: Some("UID_001".to_string()),
        TEXT_DAT: Some("".to_string()),
        CREATE_HOUR_KEY: Some("2024010100".to_string()),
        CREATE_DAY_KEY: Some("20240101".to_string()),
        CREATE_TIME: Utc::now().timestamp_millis(),
        EFUSE_EXTRA: Some(efuse_extra),
        CHIP_ID: Some("CHIP_001".to_string()),
    }, SubTestItemDetail {
        FILE_ID: Some(12345),
        ONLINE_RETEST: Some(0),
        MAX_OFFLINE_RETEST: Some(0),
        MAX_ONLINE_RETEST: Some(0),
        IS_DIE_FIRST_TEST: Some(1),
        IS_DIE_FINAL_TEST: Some(1),
        IS_FIRST_TEST: Some(1),
        IS_FINAL_TEST: Some(1),
        IS_FIRST_TEST_IGNORE_TP: Some(1),
        IS_FINAL_TEST_IGNORE_TP: Some(1),
        IS_DUP_FIRST_TEST: Some(0),
        IS_DUP_FINAL_TEST: Some(0),
        IS_DUP_FIRST_TEST_IGNORE_TP: Some(0),
        IS_DUP_FINAL_TEST_IGNORE_TP: Some(0),
        TEST_SUITE: Some("TEST_SUITE_1".to_string()),
        CONDITION_SET: None,
        TEST_NUM: Some(1001),
        TEST_TXT: Some("VDD_TEST".to_string()),
        TEST_ITEM: Some("VDD_VOLTAGE".to_string()),
        IS_DIE_FIRST_TEST_ITEM: Some(1),
        TESTITEM_TYPE: Some("P".to_string()),
        TEST_FLG: Some("PASS".to_string()),
        PARM_FLG: Some("NORMAL".to_string()),
        TEST_STATE: Some("EXECUTED".to_string()),
        TEST_VALUE: Some(3.3),
        UNITS: Some("V".to_string()),
        TEST_RESULT: Some(1),
        ORIGIN_TEST_VALUE: Some(3.3),
        ORIGIN_UNITS: Some("V".to_string()),
        TEST_ORDER: Some(1),
        ALARM_ID: Some("".to_string()),
        OPT_FLG: Some("".to_string()),
        RES_SCAL: Some(0),
        NUM_TEST: Some(1),
        LLM_SCAL: Some(0),
        HLM_SCAL: Some(0),
        LO_LIMIT: Some(3.0),
        HI_LIMIT: Some(3.6),
        ORIGIN_HI_LIMIT: Some(3.6),
        ORIGIN_LO_LIMIT: Some(3.0),
        C_RESFMT: Some("%.3f".to_string()),
        C_LLMFMT: Some("%.3f".to_string()),
        C_HLMFMT: Some("%.3f".to_string()),
        LO_SPEC: Some(3.0),
        HI_SPEC: Some(3.6),
        HBIN_NUM: Some(1),
        SBIN_NUM: Some(1),
        SBIN_PF: Some("P".to_string()),
        SBIN_NAM: Some("PASS".to_string()),
        HBIN_PF: Some("P".to_string()),
        HBIN_NAM: Some("PASS".to_string()),
        HBIN: Some("1".to_string()),
        SBIN: Some("1".to_string()),
        TEST_HEAD: Some(1),
        PART_FLG: Some("NORMAL".to_string()),
        PART_ID: Some("PART_001".to_string()),
        C_PART_ID: Some(1),
        ECID: Some("ECID_001".to_string()),
        ECID_EXT: Some("".to_string()),
        ECID_EXTRA: None,
        IS_STANDARD_ECID: Some(1),
        X_COORD: Some(100),
        Y_COORD: Some(200),
        DIE_X: Some(10),
        DIE_Y: Some(20),
        TEST_TIME: Some(1000),
        PART_TXT: Some("PART_TEXT".to_string()),
        PART_FIX: Some("PART_FIX".to_string()),
        SITE: Some(1),
        TOUCH_DOWN_ID: Some(1),
        WAFER_LOT_ID: Some("TEST_WAFER_LOT".to_string()),
        WAFER_ID: Some("WAFER_001".to_string()),
        WAFER_NO: Some("W001".to_string()),
        RETICLE_T_X: Some(0),
        RETICLE_T_Y: Some(0),
        RETICLE_X: Some(0),
        RETICLE_Y: Some(0),
        SITE_ID: Some("SITE_001".to_string()),
        VECT_NAM: Some("VECTOR_001".to_string()),
        TIME_SET: Some("TIMESET_001".to_string()),
        NUM_FAIL: Some(0),
        FAIL_PIN: Some("".to_string()),
        CYCL_CNT: Some(1),
        REPT_CNT: Some(1),
        LONG_ATTRIBUTE_SET: None,
        STRING_ATTRIBUTE_SET: None,
        FLOAT_ATTRIBUTE_SET: None,
        UID: Some("UID_001".to_string()),
        TEXT_DAT: Some("".to_string()),
        CREATE_HOUR_KEY: Some("2024010100".to_string()),
        CREATE_DAY_KEY: Some("20240101".to_string()),
        CREATE_TIME: Utc::now().timestamp_millis(),
        EFUSE_EXTRA: None,
        CHIP_ID: Some("CHIP_001".to_string()),
    }]
}

async fn test_parquet_operations(test_data: &Vec<SubTestItemDetail>) -> Result<(), Box<dyn std::error::Error>> {
    println!("开始测试parquet文件操作...");

    // 创建测试目录
    let test_dir = "./test_output";
    std::fs::create_dir_all(test_dir)?;

    let parquet_file = format!("{}/test_item_detail.parquet", test_dir);

    // 写入parquet文件
    println!("写入parquet文件: {}", parquet_file);
    write_parquet(&parquet_file, test_data, None);

    // 读取parquet文件 - 注意read_parquet需要目录路径，不是文件路径
    println!("读取parquet文件目录: {}", test_dir);
    let read_data: Vec<SubTestItemDetail> = read_parquet(test_dir, None);

    println!("写入数据量: {}, 读取数据量: {}", test_data.len(), read_data.len());
    assert_eq!(test_data.len(), read_data.len());

    println!("parquet文件操作测试成功！");
    Ok(())
}
