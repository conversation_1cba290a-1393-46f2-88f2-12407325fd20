use crate::constant::file_type::PARQUET;
use crate::hdfs_provider::{HdfsConfig, HdfsProvider};
use crate::RecordBatchWrapper;
use log::{info, warn, error};
use parquet::arrow::arrow_reader::ParquetRecordBatchReaderBuilder;
use parquet::basic::{Compression, ZstdLevel};
use parquet::file::properties::WriterProperties;
use std::fs::File;
use std::fs::{self, DirEntry};
use std::path::{Path, PathBuf};
use uuid::Uuid;
use tokio::runtime::Runtime;

pub fn write_parquet<T>(dir_path: &str, data: &Vec<T>, hdfs_config: Option<&HdfsConfig>)
where
    T: RecordBatchWrapper,
{
    if data.is_empty() {
        warn!("Cannot write parquet file with empty data: {}", dir_path);
        return;
    }

    let record_batch = match T::to_record_batch(data) {
        Ok(batch) => batch,
        Err(e) => {
            error!("Failed to convert data to record batch: {}", e);
            return;
        }
    };
    
    info!("record_batch num_rows: {:?}", record_batch.num_rows());

    if record_batch.num_rows() == 0 {
        warn!("Cannot write parquet file with empty record batch: {}", dir_path);
        return;
    }

    // 生成临时目录路径
    let temp_uuid = Uuid::new_v4().to_string();
    let temp_dir = format!("/tmp/{}", temp_uuid);
    let temp_file_path = format!("{}/data.parquet", temp_dir);
    
    // 确保在函数结束时清理临时目录
    let _cleanup_guard = TempDirCleanup::new(&temp_dir);
    
    // 创建临时目录
    if let Err(e) = fs::create_dir_all(&temp_dir) {
        error!("Failed to create temp directory {}: {}", temp_dir, e);
        return;
    }
    
    // 写入数据到临时文件
    let file = match File::create(&temp_file_path) {
        Ok(f) => f,
        Err(e) => {
            error!("Failed to create temp file {}: {}", temp_file_path, e);
            return;
        }
    };

    let props = WriterProperties::builder()
        .set_compression(Compression::ZSTD(ZstdLevel::default()))
        .set_dictionary_enabled(true)
        .set_max_row_group_size(1 * 1024 * 1024)
        .build();

    let mut writer = match parquet::arrow::ArrowWriter::try_new(file, record_batch.schema(), Some(props)) {
        Ok(w) => w,
        Err(e) => {
            error!("Failed to create parquet writer: {}", e);
            return;
        }
    };
    
    if let Err(e) = writer.write(&record_batch) {
        error!("Failed to write record batch: {}", e);
        return;
    }
    
    if let Err(e) = writer.close() {
        error!("Failed to close parquet writer: {}", e);
        return;
    }
    
    info!("Successfully wrote parquet data to temp file: {}", temp_file_path);
    
    // 根据hdfs_config决定后续操作
    if let Some(config) = hdfs_config {
        // 使用HDFS：上传到HDFS
        let rt = Runtime::new().unwrap();
        rt.block_on(async {
            match HdfsProvider::new(config.clone()) {
                Ok(hdfs_provider) => {
                    if let Err(e) = hdfs_provider.upload(&temp_file_path, dir_path).await {
                        error!("Failed to upload to HDFS {}: {}", dir_path, e);
                    } else {
                        info!("Successfully uploaded parquet file to HDFS: {}", dir_path);
                    }
                },
                Err(e) => {
                    error!("Failed to create HDFS provider: {}", e);
                }
            }
        });
    } else {
        // 本地操作：移动到目标目录
        if let Some(parent) = Path::new(dir_path).parent() {
            if let Err(e) = fs::create_dir_all(parent) {
                error!("Failed to create target directory {}: {}", parent.display(), e);
                return;
            }
        }
        
        if let Err(e) = fs::rename(&temp_file_path, dir_path) {
            error!("Failed to move file from {} to {}: {}", temp_file_path, dir_path, e);
        } else {
            info!("Successfully moved parquet file to: {}", dir_path);
        }
    }
}

pub fn read_parquet<T>(dir_path: &str, hdfs_config: Option<&HdfsConfig>) -> Vec<T>
where
    T: RecordBatchWrapper,
{
    let actual_read_path = if let Some(config) = hdfs_config {
        // 使用HDFS：下载到临时目录
        let temp_uuid = Uuid::new_v4().to_string();
        let temp_dir = format!("/tmp/{}", temp_uuid);
        
        // 确保在函数结束时清理临时目录
        let _cleanup_guard = TempDirCleanup::new(&temp_dir);
        
        // 创建临时目录
        if let Err(e) = fs::create_dir_all(&temp_dir) {
            error!("Failed to create temp directory {}: {}", temp_dir, e);
            return Vec::new();
        }
        
        let rt = Runtime::new().unwrap();
        let download_result = rt.block_on(async {
            match HdfsProvider::new(config.clone()) {
                Ok(hdfs_provider) => {
                    hdfs_provider.download(dir_path, &temp_dir).await
                },
                Err(e) => {
                    error!("Failed to create HDFS provider: {}", e);
                    return Err(e);
                }
            }
        });
        
        if let Err(e) = download_result {
            error!("Failed to download from HDFS {}: {}", dir_path, e);
            return Vec::new();
        }
        
        info!("Successfully downloaded parquet data from HDFS to temp dir: {}", temp_dir);
        temp_dir
    } else {
        // 本地操作：直接使用给定路径
        dir_path.to_string()
    };
    
    // 读取parquet文件
    let parquet_files = match find_parquet_files(&actual_read_path) {
        Ok(files) => files,
        Err(e) => {
            error!("Failed to find parquet files in {}: {}", actual_read_path, e);
            return Vec::new();
        }
    };

    if parquet_files.is_empty() {
        warn!("No parquet files found in directory: {}", actual_read_path);
        return Vec::new();
    }

    // 读取并合并所有RecordBatch
    let mut all_batch = Vec::new();
    for file_path in parquet_files {
        let file = match File::open(&file_path) {
            Ok(f) => f,
            Err(e) => {
                error!("Failed to open parquet file {:?}: {}", file_path, e);
                continue;
            }
        };
        
        let reader_builder = match ParquetRecordBatchReaderBuilder::try_new(file) {
            Ok(builder) => builder,
            Err(e) => {
                error!("Failed to create parquet reader for {:?}: {}", file_path, e);
                continue;
            }
        };
        
        let mut reader = match reader_builder.build() {
            Ok(r) => r,
            Err(e) => {
                error!("Failed to build parquet reader for {:?}: {}", file_path, e);
                continue;
            }
        };
        
        while let Some(batch_result) = reader.next() {
            match batch_result {
                Ok(batch) => all_batch.push(batch),
                Err(e) => {
                    error!("Failed to read batch from {:?}: {}", file_path, e);
                    break;
                }
            }
        }
    }

    if all_batch.is_empty() {
        warn!("No record batches read from parquet files");
        return Vec::new();
    }

    let mut result = Vec::new();
    for batch in &all_batch {
        match T::from_record_batch(batch) {
            Ok(records) => result.extend(records),
            Err(e) => {
                error!("Failed to convert record batch to data: {}", e);
                continue;
            }
        }
    }
    
    info!("读取出: {}条记录", result.len());
    result
}

fn find_parquet_files(dir: &str) -> Result<Vec<PathBuf>, Box<dyn std::error::Error>> {
    let mut result = Vec::new();
    visit_dirs(Path::new(dir), &mut |entry| {
        if let Some(ext) = entry.path().extension() {
            if ext == PARQUET {
                result.push(entry.path().to_path_buf());
            }
        }
    })?;
    Ok(result)
}

fn visit_dirs(dir: &Path, cb: &mut dyn FnMut(&DirEntry)) -> std::io::Result<()> {
    if dir.is_dir() {
        for entry in fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            if path.is_dir() {
                visit_dirs(&path, cb)?;
            } else {
                cb(&entry);
            }
        }
    }
    Ok(())
}

/// 临时目录清理守卫，确保在作用域结束时自动清理临时目录
struct TempDirCleanup {
    temp_dir: String,
}

impl TempDirCleanup {
    fn new(temp_dir: &str) -> Self {
        Self {
            temp_dir: temp_dir.to_string(),
        }
    }
}

impl Drop for TempDirCleanup {
    fn drop(&mut self) {
        if Path::new(&self.temp_dir).exists() {
            if let Err(e) = fs::remove_dir_all(&self.temp_dir) {
                error!("Failed to cleanup temp directory {}: {}", self.temp_dir, e);
            } else {
                info!("Successfully cleaned up temp directory: {}", self.temp_dir);
            }
        }
    }
}
